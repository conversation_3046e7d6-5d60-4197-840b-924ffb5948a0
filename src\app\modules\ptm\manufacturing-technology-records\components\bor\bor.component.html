<ng-container>
    <div class="p-fluid tw-space-y-2">
        <app-info-share [title]="title"></app-info-share>
    </div>
    <div class="tw-flex tw-items-center tw-justify-between tw-gap-4 tw-flex-wrap tw-mb-[15px] tw-pb-[10px] border-bottom">
        <label class="tw-font-semibold tw-text-base label">Chi tiết BOR</label>

        <div class="tw-flex tw-gap-2">
            <button pButton type="button" label="Track Changes" class="p-button-sm p-button-primary tw-h-10 tw-whitespace-nowrap"></button>
            <button pButton type="button" label="Xuất excel" class="p-button-sm p-button-secondary tw-h-10 tw-whitespace-nowrap"></button>
        </div>
    </div>
    <div class="tw-flex tw-items-center tw-justify-between tw-gap-4 tw-flex-wrap tw-mb-[15px] tw-pb-[10px] border-bottom">
        <label class="tw-font-semibold tw-text-base label"><PERSON><PERSON>ng định mức MAN</label>

        <div class="tw-flex tw-gap-2">
            <button pButton type="button" label="Xuất excel" class="p-button-sm p-button-secondary tw-h-10 tw-whitespace-nowrap"></button>
        </div>

        <div class="tw-grid tw-w-full [grid-template-columns:auto_1fr] tw-gap-y-4 tw-gap-x-4">
            <div class="tw-font-semibold">Trạng thái:</div>
            <div class="tw-font-semibold">
                {{ STATUS_MAP[detailBor?.instructionInfo?.status] ?? 'Draft' }}
            </div>
            <div class="tw-font-semibold">Người soát xét:</div>
            <div class="tw-max-w-[500px]">
                <app-form #form [formGroup]="formGroup">
                    <app-custom-form-item [control]="">
                        <app-combobox-nonRSQL
                            #userApproval
                            [fetchOnInit]="false"
                            type="select"
                            fieldValue="id"
                            fieldLabel="email"
                            url="/auth/api/users/simple-search"
                            param="query"
                            [additionalParams]="{ page: 0, size: 100 }"
                        >
                        </app-combobox-nonRSQL>
                    </app-custom-form-item>
                </app-form>
            </div>
        </div>

        <div #contaninerListMan>
            <app-form-item label="" [disableAutoErrorMessage]="true">
                <app-form #form [formGroup]="formGroup" layout="vertical">
                    <div formArrayName="listMan">
                        <p-panel [toggleable]="true">
                            <ng-template pTemplate="icons">
                                <button
                                    pButton
                                    type="button"
                                    class="p-button-outlined p-button-sm toggle-btn pi pi-arrows-alt"
                                    appFullscreenToggle
                                    [target]="contaninerListMan"
                                ></button>
                            </ng-template>
                            <p-table styleClass="p-datatable-gridlines" [value]="listMan.controls">
                                <ng-template pTemplate="header">
                                    <tr>
                                        <th style="min-width: 9rem">Level</th>
                                        <th style="min-width: 9rem">Công đoạn</th>
                                        <th style="min-width: 9rem">Định mức bộ phận sản xuất trực tiếp (man.h)</th>
                                        <th style="min-width: 9rem">Định mức bộ phận IPQC (man.h)</th>
                                        <th style="min-width: 9rem">Định mức bộ phận FQC (man.h)</th>
                                        <th style="min-width: 9rem">Định mức bộ phận sửa chữa (man.h)</th>
                                        <th style="min-width: 9rem">Định mức bộ phận kỹ thuật (man.h)</th>
                                        <th style="min-width: 9rem">Định mức bộ phận vật tư (man.h)</th>
                                        <th style="max-width: 5rem">Thao tác</th>
                                    </tr>
                                </ng-template>
                                <ng-template pTemplate="body" let-item let-rowIndex="rowIndex">
                                    <tr [formGroupName]="rowIndex" *ngIf="item.get('action')?.value !== 3">
                                        <td>
                                            <app-form-item [disableAutoErrorMessage]="true" label=""> {{ rowIndex + 1 }}</app-form-item>
                                        </td>

                                        <td>
                                            <app-form-item label="">
                                                <div class="tw-flex tw-justify-between tw-items-center tw-gap-2">Công đoạn nè</div>
                                            </app-form-item>
                                        </td>
                                        <td>
                                            <app-form-item label="" [disableAutoErrorMessage]="true">
                                                <div class="tw-flex tw-justify-between tw-items-center tw-gap-2">
                                                    <input type="text" class="tw-w-full tw-flex-1" pInputText maxlength="10" formControlName="manDirect" />
                                                    <div class="tw-cursor-pointer">
                                                        <p-overlayPanel #op [dismissable]="true" [showCloseIcon]="true" styleClass="tw-p-4 tw-w-72">
                                                            <div class="tw-space-y-4">
                                                                <!-- Người tính -->
                                                                <div>
                                                                    <label class="tw-block">Người tính</label>
                                                                    <input
                                                                        type="text"
                                                                        pInputText
                                                                        class="tw-w-full"
                                                                        placeholder="Nhập tên người tính"
                                                                        formControlName="manDirectComputedBy"
                                                                    />
                                                                </div>

                                                                <!-- Ngày tính -->
                                                                <div>
                                                                    <label class="tw-block">Ngày tính</label>
                                                                    <p-calendar
                                                                        class="tw-w-full"
                                                                        dateFormat="dd/mm/yy"
                                                                        inputStyleClass="tw-w-full"
                                                                        formControlName="manDirectComputedAt"
                                                                    />
                                                                </div>
                                                                <div class="tw-flex tw-justify-center tw-gap-4 tw-pt-2">
                                                                    <button pButton type="button" label="Lưu" class="p-button-sm p-button-success"></button>
                                                                    <button
                                                                        pButton
                                                                        type="button"
                                                                        label="Hủy"
                                                                        class="p-button-sm p-button-secondary"
                                                                        (click)="op.hide()"
                                                                    ></button>
                                                                </div>
                                                            </div>
                                                        </p-overlayPanel>

                                                        <div (click)="op.toggle($event)" class="tw-cursor-pointer">
                                                            <i class="pi pi-user-plus"></i>
                                                        </div>
                                                    </div>
                                                </div>
                                            </app-form-item>
                                        </td>
                                        <td>
                                            <app-form-item label="" [disableAutoErrorMessage]="true">
                                                <div class="tw-flex tw-justify-between tw-items-center tw-gap-2">
                                                    <input type="text" class="tw-w-full tw-flex-1" pInputText maxlength="10" formControlName="manIpqc" />
                                                    <div class="tw-cursor-pointer">
                                                        <p-overlayPanel #op [dismissable]="true" [showCloseIcon]="true" styleClass="tw-p-4 tw-w-72">
                                                            <div class="tw-space-y-4">
                                                                <!-- Người tính -->
                                                                <div>
                                                                    <label class="tw-block">Người tính</label>
                                                                    <input
                                                                        type="text"
                                                                        pInputText
                                                                        class="tw-w-full"
                                                                        placeholder="Nhập tên người tính"
                                                                        formControlName="manIpqcComputedBy"
                                                                    />
                                                                </div>

                                                                <!-- Ngày tính -->
                                                                <div>
                                                                    <label class="tw-block">Ngày tính</label>
                                                                    <p-calendar
                                                                        class="tw-w-full"
                                                                        dateFormat="dd/mm/yy"
                                                                        inputStyleClass="tw-w-full"
                                                                        formControlName="manIpqcComputedAt"
                                                                    />
                                                                </div>
                                                                <div class="tw-flex tw-justify-center tw-gap-4 tw-pt-2">
                                                                    <button pButton type="button" label="Lưu" class="p-button-sm p-button-success"></button>
                                                                    <button
                                                                        pButton
                                                                        type="button"
                                                                        label="Hủy"
                                                                        class="p-button-sm p-button-secondary"
                                                                        (click)="op.hide()"
                                                                    ></button>
                                                                </div>
                                                            </div>
                                                        </p-overlayPanel>

                                                        <div (click)="op.toggle($event)" class="tw-cursor-pointer">
                                                            <i class="pi pi-user-plus"></i>
                                                        </div>
                                                    </div>
                                                </div>
                                            </app-form-item>
                                        </td>
                                        <td>
                                            <app-form-item label="" [disableAutoErrorMessage]="true">
                                                <div class="tw-flex tw-justify-between tw-items-center tw-gap-2">
                                                    <input type="text" class="tw-w-full tw-flex-1" pInputText maxlength="10" formControlName="manFqc" />
                                                    <div class="tw-cursor-pointer">
                                                        <p-overlayPanel #op [dismissable]="true" [showCloseIcon]="true" styleClass="tw-p-4 tw-w-72">
                                                            <div class="tw-space-y-4">
                                                                <!-- Người tính -->
                                                                <div>
                                                                    <label class="tw-block">Người tính</label>
                                                                    <input
                                                                        type="text"
                                                                        pInputText
                                                                        class="tw-w-full"
                                                                        placeholder="Nhập tên người tính"
                                                                        formControlName="manFqcComputedBy"
                                                                    />
                                                                </div>

                                                                <!-- Ngày tính -->
                                                                <div>
                                                                    <label class="tw-block">Ngày tính</label>
                                                                    <p-calendar
                                                                        class="tw-w-full"
                                                                        dateFormat="dd/mm/yy"
                                                                        inputStyleClass="tw-w-full"
                                                                        formControlName="manFqcComputedAt"
                                                                    />
                                                                </div>
                                                                <div class="tw-flex tw-justify-center tw-gap-4 tw-pt-2">
                                                                    <button pButton type="button" label="Lưu" class="p-button-sm p-button-success"></button>
                                                                    <button
                                                                        pButton
                                                                        type="button"
                                                                        label="Hủy"
                                                                        class="p-button-sm p-button-secondary"
                                                                        (click)="op.hide()"
                                                                    ></button>
                                                                </div>
                                                            </div>
                                                        </p-overlayPanel>

                                                        <div (click)="op.toggle($event)" class="tw-cursor-pointer">
                                                            <i class="pi pi-user-plus"></i>
                                                        </div>
                                                    </div>
                                                </div>
                                            </app-form-item>
                                        </td>
                                        <td>
                                            <app-form-item label="" [disableAutoErrorMessage]="true">
                                                <div class="tw-flex tw-justify-between tw-items-center tw-gap-2">
                                                    <input type="text" class="tw-w-full tw-flex-1" pInputText maxlength="10" formControlName="manRepair" />
                                                    <div class="tw-cursor-pointer">
                                                        <p-overlayPanel #op [dismissable]="true" [showCloseIcon]="true" styleClass="tw-p-4 tw-w-72">
                                                            <div class="tw-space-y-4">
                                                                <!-- Người tính -->
                                                                <div>
                                                                    <label class="tw-block">Người tính</label>
                                                                    <input
                                                                        type="text"
                                                                        pInputText
                                                                        class="tw-w-full"
                                                                        placeholder="Nhập tên người tính"
                                                                        formControlName="manRepairComputedBy"
                                                                    />
                                                                </div>

                                                                <!-- Ngày tính -->
                                                                <div>
                                                                    <label class="tw-block">Ngày tính</label>
                                                                    <p-calendar
                                                                        class="tw-w-full"
                                                                        dateFormat="dd/mm/yy"
                                                                        inputStyleClass="tw-w-full"
                                                                        formControlName="manRepairComputedAt"
                                                                    />
                                                                </div>
                                                                <div class="tw-flex tw-justify-center tw-gap-4 tw-pt-2">
                                                                    <button pButton type="button" label="Lưu" class="p-button-sm p-button-success"></button>
                                                                    <button
                                                                        pButton
                                                                        type="button"
                                                                        label="Hủy"
                                                                        class="p-button-sm p-button-secondary"
                                                                        (click)="op.hide()"
                                                                    ></button>
                                                                </div>
                                                            </div>
                                                        </p-overlayPanel>

                                                        <div (click)="op.toggle($event)" class="tw-cursor-pointer">
                                                            <i class="pi pi-user-plus"></i>
                                                        </div>
                                                    </div>
                                                </div>
                                            </app-form-item>
                                        </td>
                                        <td>
                                            <app-form-item label="" [disableAutoErrorMessage]="true">
                                                <div class="tw-flex tw-justify-between tw-items-center tw-gap-2">
                                                    <input type="text" class="tw-w-full tw-flex-1" pInputText maxlength="10" formControlName="manEngineer" />
                                                    <div class="tw-cursor-pointer">
                                                        <p-overlayPanel #op [dismissable]="true" [showCloseIcon]="true" styleClass="tw-p-4 tw-w-72">
                                                            <div class="tw-space-y-4">
                                                                <!-- Người tính -->
                                                                <div>
                                                                    <label class="tw-block">Người tính</label>
                                                                    <input
                                                                        type="text"
                                                                        pInputText
                                                                        class="tw-w-full"
                                                                        placeholder="Nhập tên người tính"
                                                                        formControlName="manEngineerComputedBy"
                                                                    />
                                                                </div>

                                                                <!-- Ngày tính -->
                                                                <div>
                                                                    <label class="tw-block">Ngày tính</label>
                                                                    <p-calendar
                                                                        class="tw-w-full"
                                                                        dateFormat="dd/mm/yy"
                                                                        inputStyleClass="tw-w-full"
                                                                        formControlName="manEngineerComputedAt"
                                                                    />
                                                                </div>
                                                                <div class="tw-flex tw-justify-center tw-gap-4 tw-pt-2">
                                                                    <button pButton type="button" label="Lưu" class="p-button-sm p-button-success"></button>
                                                                    <button
                                                                        pButton
                                                                        type="button"
                                                                        label="Hủy"
                                                                        class="p-button-sm p-button-secondary"
                                                                        (click)="op.hide()"
                                                                    ></button>
                                                                </div>
                                                            </div>
                                                        </p-overlayPanel>

                                                        <div (click)="op.toggle($event)" class="tw-cursor-pointer">
                                                            <i class="pi pi-user-plus"></i>
                                                        </div>
                                                    </div>
                                                </div>
                                            </app-form-item>
                                        </td>
                                        <td>
                                            <app-form-item label="" [disableAutoErrorMessage]="true">
                                                <div class="tw-flex tw-justify-between tw-items-center tw-gap-2">
                                                    <input type="text" class="tw-w-full tw-flex-1" pInputText maxlength="10" formControlName="manMaterial" />
                                                    <div class="tw-cursor-pointer">
                                                        <p-overlayPanel #op [dismissable]="true" [showCloseIcon]="true" styleClass="tw-p-4 tw-w-72">
                                                            <div class="tw-space-y-4">
                                                                <!-- Người tính -->
                                                                <div>
                                                                    <label class="tw-block">Người tính</label>
                                                                    <input
                                                                        type="text"
                                                                        pInputText
                                                                        class="tw-w-full"
                                                                        placeholder="Nhập tên người tính"
                                                                        formControlName="manMaterialComputedBy"
                                                                    />
                                                                </div>

                                                                <!-- Ngày tính -->
                                                                <div>
                                                                    <label class="tw-block">Ngày tính</label>
                                                                    <p-calendar
                                                                        class="tw-w-full"
                                                                        dateFormat="dd/mm/yy"
                                                                        inputStyleClass="tw-w-full"
                                                                        formControlName="manMaterialComputedAt"
                                                                    />
                                                                </div>
                                                                <div class="tw-flex tw-justify-center tw-gap-4 tw-pt-2">
                                                                    <button pButton type="button" label="Lưu" class="p-button-sm p-button-success"></button>
                                                                    <button
                                                                        pButton
                                                                        type="button"
                                                                        label="Hủy"
                                                                        class="p-button-sm p-button-secondary"
                                                                        (click)="op.hide()"
                                                                    ></button>
                                                                </div>
                                                            </div>
                                                        </p-overlayPanel>

                                                        <div (click)="op.toggle($event)" class="tw-cursor-pointer">
                                                            <i class="pi pi-user-plus"></i>
                                                        </div>
                                                    </div>
                                                </div>
                                            </app-form-item>
                                        </td>
                                        <td>
                                            <div class="tw-flex tw-flex-nowrap tw-gap-3">
                                                <button
                                                    [disabled]="mode === 'view'"
                                                    class="p-link tw-p-2 bg-red-300 hover:tw-bg-red-400 tw-text-white"
                                                    pTooltip="Hủy"
                                                    tooltipPosition="top"
                                                    type="button"
                                                    (click)="removeItem(rowIndex)"
                                                >
                                                    <span class="pi pi-times"></span>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                </ng-template>
                            </p-table>
                            <div class="tw-mt-3">
                                <p-button
                                    [disabled]="mode === 'view'"
                                    label="Thêm dòng"
                                    icon="pi pi-plus"
                                    severity="info"
                                    size="small"
                                    (click)="addItem(1)"
                                ></p-button>
                            </div>
                        </p-panel>
                    </div>
                </app-form>
            </app-form-item>
        </div>
    </div>

    <div class="tw-flex tw-items-center tw-justify-between tw-gap-4 tw-flex-wrap tw-mb-[15px] tw-pb-[10px] border-bottom">
        <label class="tw-font-semibold tw-text-base label">Bảng định mức LINE, MACHINE</label>

        <div class="tw-flex tw-gap-2">
            <button pButton type="button" label="Xuất excel" class="p-button-sm p-button-secondary tw-h-10 tw-whitespace-nowrap"></button>
        </div>

        <div class="tw-grid tw-w-full [grid-template-columns:auto_1fr] tw-gap-y-4 tw-gap-x-4">
            <div class="tw-font-semibold">Trạng thái:</div>
            <div class="tw-font-semibold">
                {{ STATUS_MAP[detailBor?.instructionInfo?.status] ?? 'Draft' }}
            </div>
            <div class="tw-font-semibold">Người soát xét:</div>
            <div class="tw-max-w-[500px]">
                <app-form #form [formGroup]="formGroup">
                    <app-custom-form-item [control]="">
                        <app-combobox-nonRSQL
                            #userApproval
                            [fetchOnInit]="false"
                            type="select"
                            fieldValue="id"
                            fieldLabel="email"
                            url="/auth/api/users/simple-search"
                            param="query"
                            [additionalParams]="{ page: 0, size: 100 }"
                        >
                        </app-combobox-nonRSQL>
                    </app-custom-form-item>
                </app-form>
            </div>
        </div>

        <div #contaninerListLine>
            <app-form-item label="" [disableAutoErrorMessage]="true">
                <app-form #form [formGroup]="formGroup" layout="vertical">
                    <div formArrayName="listLine">
                        <p-panel [toggleable]="true">
                            <ng-template pTemplate="icons">
                                <button
                                    pButton
                                    type="button"
                                    class="p-button-outlined p-button-sm toggle-btn pi pi-arrows-alt"
                                    appFullscreenToggle
                                    [target]="contaninerListLine"
                                ></button>
                            </ng-template>
                            <p-table styleClass="p-datatable-gridlines" [value]="listLine.controls">
                                <ng-template pTemplate="header">
                                    <tr>
                                        <th style="min-width: 9rem">STT</th>
                                        <th style="min-width: 9rem">Danh mục MMTB chính</th>
                                        <th style="min-width: 9rem">Công đoạn</th>
                                        <th style="min-width: 9rem">Định mức</th>
                                        <th style="min-width: 9rem">Đơn vị</th>
                                        <th style="min-width: 9rem">Người tính</th>
                                        <th style="min-width: 9rem">Ngày tính</th>
                                        <th style="max-width: 5rem">Thao tác</th>
                                    </tr>
                                </ng-template>
                                <ng-template pTemplate="body" let-item let-rowIndex="rowIndex">
                                    <tr [formGroupName]="rowIndex" *ngIf="item.get('action')?.value !== 3">
                                        <td>
                                            <app-form-item [disableAutoErrorMessage]="true" label=""> {{ rowIndex + 1 }}</app-form-item>
                                        </td>

                                        <td>
                                            <app-form-item label="" [disableAutoErrorMessage]="true">
                                                <input type="text" class="tw-w-full" pInputText maxlength="4" formControlName="equipId" />
                                            </app-form-item>
                                        </td>
                                        <td>
                                            <app-form-item label="" [disableAutoErrorMessage]="true">
                                                <p-dropdown
                                                    [options]="sectionBor"
                                                    formControlName="section"
                                                    optionLabel="label"
                                                    optionValue="value"
                                                    class="tw-w-full"
                                                    [showClear]="true"
                                                    appendTo="body"
                                                />
                                            </app-form-item>
                                        </td>
                                        <td>
                                            <app-form-item label="" [disableAutoErrorMessage]="true">
                                                <input type="text" class="tw-w-full" pInputText maxlength="10" formControlName="norm" />
                                            </app-form-item>
                                        </td>
                                        <td>
                                            <app-form-item label="" [disableAutoErrorMessage]="true">
                                                <p-dropdown
                                                    [options]="unitBor"
                                                    formControlName="unit"
                                                    optionLabel="label"
                                                    optionValue="value"
                                                    class="tw-w-full"
                                                    [showClear]="true"
                                                    appendTo="body"
                                                />
                                            </app-form-item>
                                        </td>
                                        <td>
                                            <app-form-item label="" [disableAutoErrorMessage]="true">
                                                <p-dropdown
                                                    [options]="productProcessType"
                                                    formControlName="computedAt"
                                                    optionLabel="label"
                                                    optionValue="value"
                                                    class="tw-w-full"
                                                    [showClear]="true"
                                                    appendTo="body"
                                                />
                                            </app-form-item>
                                        </td>
                                        <td>
                                            <app-form-item label="" [disableAutoErrorMessage]="true">
                                                <p-checkbox
                                                    name="online"
                                                    formControlName="computedBy"
                                                    [binary]="true"
                                                    (onChange)="item.updateValueAndValidity()"
                                                ></p-checkbox>
                                            </app-form-item>
                                        </td>
                                        <td>
                                            <div class="tw-flex tw-flex-nowrap tw-gap-3">
                                                <button
                                                    [disabled]="mode === 'view'"
                                                    class="p-link tw-p-2 bg-red-300 hover:tw-bg-red-400 tw-text-white"
                                                    pTooltip="Hủy"
                                                    tooltipPosition="top"
                                                    type="button"
                                                    (click)="removeItem(rowIndex)"
                                                >
                                                    <span class="pi pi-times"></span>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                </ng-template>
                            </p-table>
                            <div class="tw-mt-3">
                                <p-button
                                    [disabled]="mode === 'view'"
                                    label="Thêm dòng"
                                    icon="pi pi-plus"
                                    severity="info"
                                    size="small"
                                    (click)="addItem(2)"
                                ></p-button>
                            </div>
                        </p-panel>
                    </div>
                </app-form>
            </app-form-item>
        </div>
    </div>

    <div class="tw-flex tw-items-center tw-justify-between tw-gap-4 tw-flex-wrap tw-mb-[15px] tw-pb-[10px] border-bottom">
        <label class="tw-font-semibold tw-text-base label">Bảng định mức Material</label>

        <div class="tw-flex tw-gap-2">
            <button pButton type="button" label="Xuất excel" class="p-button-sm p-button-secondary tw-h-10 tw-whitespace-nowrap"></button>
        </div>

        <div class="tw-grid tw-w-full [grid-template-columns:auto_1fr] tw-gap-y-4 tw-gap-x-4">
            <div class="tw-font-semibold">Trạng thái:</div>
            <div class="tw-font-semibold">
                {{ STATUS_MAP[detailBor?.instructionInfo?.status] ?? 'Draft' }}
            </div>
            <div class="tw-font-semibold">Người soát xét:</div>
            <div class="tw-max-w-[500px]">
                <app-form #form [formGroup]="formGroup">
                    <app-custom-form-item [control]="">
                        <app-combobox-nonRSQL
                            #userApproval
                            [fetchOnInit]="false"
                            type="select"
                            fieldValue="id"
                            fieldLabel="email"
                            url="/auth/api/users/simple-search"
                            param="query"
                            [additionalParams]="{ page: 0, size: 100 }"
                        >
                        </app-combobox-nonRSQL>
                    </app-custom-form-item>
                </app-form>
            </div>
        </div>

        <div #contaninerListMaterial>
            <app-form-item label="" [disableAutoErrorMessage]="true">
                <app-form #form [formGroup]="formGroup" layout="vertical">
                    <div formArrayName="listMaterial">
                        <p-panel [toggleable]="true">
                            <ng-template pTemplate="icons">
                                <button
                                    pButton
                                    type="button"
                                    class="p-button-outlined p-button-sm toggle-btn pi pi-arrows-alt"
                                    appFullscreenToggle
                                    [target]="contaninerListMaterial"
                                ></button>
                            </ng-template>
                            <p-table styleClass="p-datatable-gridlines" [value]="listMaterial.controls">
                                <ng-template pTemplate="header">
                                    <tr>
                                        <th style="min-width: 9rem">STT</th>
                                        <th style="min-width: 9rem">VNPT PN</th>
                                        <th style="min-width: 9rem">Old VNPT PN</th>
                                        <th style="min-width: 9rem">Description</th>
                                        <th style="min-width: 9rem">Đơn vị</th>
                                        <th style="min-width: 9rem">Công đoạn</th>
                                        <th style="min-width: 9rem">Số lượng công đoạn</th>
                                        <th style="min-width: 9rem">Reference</th>
                                        <th style="max-width: 9rem">Loại vật tư</th>
                                        <th style="min-width: 9rem">Tỷ lệ tiêu hao</th>
                                        <th style="min-width: 9rem">Ghi chú</th>
                                    </tr>
                                </ng-template>
                                <ng-template pTemplate="body" let-item let-rowIndex="rowIndex">
                                    <tr [formGroupName]="rowIndex" *ngIf="item.get('action')?.value !== 3">
                                        <td>
                                            <app-form-item [disableAutoErrorMessage]="true" label=""> {{ rowIndex + 1 }}</app-form-item>
                                        </td>

                                        <td>
                                            <app-form-item label="" [disableAutoErrorMessage]="true"> a </app-form-item>
                                        </td>
                                        <td>
                                            <app-form-item label="" [disableAutoErrorMessage]="true">
                                                <input type="text" class="tw-w-full" pInputText maxlength="1000" />
                                            </app-form-item>
                                        </td>
                                        <td>
                                            <app-form-item label="" [disableAutoErrorMessage]="true"> </app-form-item>
                                        </td>
                                        <td>
                                            <app-form-item label="" [disableAutoErrorMessage]="true">
                                                <input type="text" class="tw-w-full" pInputText maxlength="100" />
                                            </app-form-item>
                                        </td>
                                        <td>
                                            <app-form-item label="" [disableAutoErrorMessage]="true"> </app-form-item>
                                        </td>
                                        <td>
                                            <app-form-item label="" [disableAutoErrorMessage]="true"> </app-form-item>
                                        </td>
                                        <td>
                                            <app-form-item label="" [disableAutoErrorMessage]="true"> </app-form-item>
                                        </td>
                                        <td>
                                            <app-form-item label="" [disableAutoErrorMessage]="true"> </app-form-item>
                                        </td>
                                        <td>
                                            <app-form-item label="" [disableAutoErrorMessage]="true"> </app-form-item>
                                        </td>
                                        <td>
                                            <app-form-item label="" [disableAutoErrorMessage]="true">12 ghi chú </app-form-item>
                                        </td>
                                    </tr>
                                </ng-template>
                            </p-table>
                        </p-panel>
                    </div>
                </app-form>
            </app-form-item>
        </div>
    </div>

    <div class="tw-flex tw-gap-4 tw-justify-center tw-mt-4">
        <p-button
            *ngIf="
                (detailBor?.instructionInfo?.status === 1 ||
                    detailBor?.instructionInfo?.status === 4 ||
                    this.instructionId === 0 ||
                    isCreatingInstruction ||
                    !detailBor?.instructionInfo?.status) &&
                mode !== 'view'
            "
            label="Lưu"
            type="submit"
            [loading]="isSaving | async"
            (onClick)="handleSubmit($event)"
            loadingIcon="pi pi-spinner pi-spin"
        >
        </p-button>
        <button
            *ngIf="
                detailBor?.instructionInfo?.status === 1 ||
                detailBor?.instructionInfo?.status === 4 ||
                this.instructionId === 0 ||
                isCreatingInstruction ||
                !detailBor?.instructionInfo?.status
            "
            label="Gửi review"
            pButton
            type="button"
            [disabled]="isApproving | async"
            loadingIcon="pi pi-spinner pi-spin"
            class="p-button-secondary"
            (click)="handlePreview()"
        ></button>
        <p-button
            *ngIf="
                !isCreatingInstruction &&
                detailBor?.instructionInfo?.status !== 1 &&
                detailBor?.instructionInfo?.status !== 4 &&
                this.instructionId !== 0 &&
                detailBor?.instructionInfo?.status
            "
            label="Phê duyệt"
            type="button"
            [disabled]="isApproving | async"
            [loading]="isApproving | async"
            loadingIcon="pi pi-spinner pi-spin"
            (click)="handleComplete()"
        >
        </p-button>
        <button
            *ngIf="
                !isCreatingInstruction &&
                detailBor?.instructionInfo?.status !== 1 &&
                detailBor?.instructionInfo?.status !== 4 &&
                this.instructionId !== 0 &&
                detailBor?.instructionInfo?.status
            "
            label="Từ chối"
            pButton
            type="button"
            [disabled]="isApproving | async"
            loadingIcon="pi pi-spinner pi-spin"
            class="p-button-danger"
            (click)="handleReject()"
        ></button>
    </div>
</ng-container>
