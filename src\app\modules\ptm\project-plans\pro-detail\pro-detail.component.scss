.workday-dialog-content {
  padding: 0.5rem;

  h3 {
    margin-top: 1rem;
    margin-bottom: 0.5rem;
    font-size: 1.1rem;
    // color: var(--surface-700);
  }
}

.legend-section {
  margin-bottom: 1.5rem;

  .legend-items {
    margin-top: 0.5rem;
    display: flex;
    flex-direction: column;
    gap: 0.75rem;

    .legend-item {
      display: flex;
      align-items: center;
      gap: 0.75rem;

      .calendar-box {
        width: 48px;
        height: 48px;
        border: 1px solid #999;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 16px;
        font-weight: 500;
        border-radius: 4px;

        &.working {
          color: #999; // xám
        }

        &.nonworking {
          color: #2563eb; // xanh
        }
      }
    }
  }
}
:host ::ng-deep .p-dropdown {
    width: 100% !important;
}
// ::ng-deep .p-datepicker-calendar td:nth-child(1),  /* Ch<PERSON> nhật */
// ::ng-deep .p-datepicker-calendar td:nth-child(7) { /* T<PERSON><PERSON> 7 */
//   color: #2563eb;           /* Text xanh dương */
//   font-weight: bold;      /* Bo tròn cho giống ô */
// }

::ng-deep .compact-week-calendar {
  transform: scale(0.95);
  transform-origin: left top;
  width: calc(100% / 0.85);
  .p-datepicker-calendar {
    td:nth-child(1),
    td:nth-child(7) {
      color: #2563eb;           /* Text xanh dương */
      font-weight: bold;
    }
  }
}

.p-button-sm-add {
    height: 30px;
    width: 30px;
}

.tw-text-center {
    text-align: center;
}

::ng-deep .task-level-1 {
  background-color: #a5d6a7; /* Xanh lá đậm */
}

::ng-deep .task-level-2 {
  background-color: #e8f5e9; /* Xanh lá nhạt */
}

::ng-deep .task-level-3 {
  background-color: #e8f5e9; /* Xanh rất nhạt */
}

.task-qua-han {
  background-color: rgba(239, 68, 68, 0.1) !important;
  border-left: 3px solid #ef4444;
  
  &:hover {
    background-color: rgba(239, 68, 68, 0.15) !important;
  }
  
  td {
    color: #ef4444;
    font-weight: 500;
  }
}
.error-message {
  white-space: pre-line;
  // word-break: break-word;
  // overflow-wrap: break-word;
}
