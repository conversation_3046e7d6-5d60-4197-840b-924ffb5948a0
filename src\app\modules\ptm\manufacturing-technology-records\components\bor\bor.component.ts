import { Component, Input, Output, EventEmitter, OnInit, OnDestroy, ViewChild, AfterViewInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { InputTextareaModule } from 'primeng/inputtextarea';
import { InfoShareComponent } from '../info-share/info-share.component';
import { FormCustomModule } from 'src/app/shared/form-module/form.custom.module';
import { PanelModule } from 'primeng/panel';
import { TableModule } from 'primeng/table';
import { ButtonModule } from 'primeng/button';
import { BorService } from 'src/app/services/ptm/bor/bor.service';
import { MessageService } from 'primeng/api';
import { Observable, Subject, takeUntil } from 'rxjs';
import { Bor, ManDetail, LineDetail, MaterialDetail } from 'src/app/models/interface/ptm/bor';
import { TabSharedStateService } from 'src/app/services/ptm/manufacturing-technology-records/tab-shared-state/tab-shared-state.service';
import { FormArrayCustom } from 'src/app/shared/form-module/from-array.custom';
import { FormGroupCustom } from 'src/app/shared/form-module/from-group.custom';
import { FormBuilder, FormGroup, FormArray, Validators } from '@angular/forms';
import { FormComponent } from 'src/app/shared/form-module/form-base/form.component';
import { InputTextModule } from 'primeng/inputtext';
import { DropdownModule } from 'primeng/dropdown';
import { OverlayPanelModule } from 'primeng/overlaypanel';
import { CalendarModule } from 'primeng/calendar';
import { TooltipModule } from 'primeng/tooltip';
import { TAB_TYPE, SECTION_BOR, UNIT_BOR } from 'src/app/models/constant/ptm';
import { STATUS_MAP } from 'src/app/models/constant/pms';
import { ComboboxNonRSQLComponent } from 'src/app/shared/components/combobox-nonRSQL/combobox-nonRSQL.component';

@Component({
    selector: 'app-bor',
    standalone: true,
    imports: [
        CommonModule,
        FormsModule,
        InputTextareaModule,
        InfoShareComponent,
        FormCustomModule,
        PanelModule,
        TableModule,
        ButtonModule,
        InputTextModule,
        DropdownModule,
        OverlayPanelModule,
        TooltipModule,
        CalendarModule,
        ComboboxNonRSQLComponent,
    ],
    templateUrl: './bor.component.html',
    styleUrls: ['./bor.component.scss'],
})
export class BorComponent implements OnInit, OnDestroy, AfterViewInit {
    // 🧠 INPUTS từ cha truyền xuống
    @Input() title: string = '';
    @Input() content: string = '';
    @Input() config: { type: string } = { type: 'default' };
    @Input() items: string[] = [];
    @Input() editable: boolean = true;
    @Input() onSubmit!: () => void; // Callback được truyền
    @Input() currentProduct: any;
    @Input() productInstructionId: number;

    // 📤 OUTPUTS emit về cha
    @Output() submitted = new EventEmitter<any>();
    @ViewChild('form', { static: false }) formComponent!: FormComponent;
    @ViewChild('userApproval') userApproval!: ComboboxNonRSQLComponent;
    private destroy$ = new Subject<void>();
    oldBor: Bor = {
        reviewerIds: [],
        listMan: [],
        listLine: [],
        listMaterial: [],
    };
    instructionId: number;
    name: string;
    detailBor: any;
    mode: 'view' | 'create' | 'edit' = 'create';
    tabType = TAB_TYPE.bor;
    sectionBor = SECTION_BOR;
    unitBor = UNIT_BOR;
    formGroup: FormGroupCustom<Bor>;
    isCreatingInstruction = false;
    STATUS_MAP = STATUS_MAP;

    constructor(
        private fb: FormBuilder,
        private borService: BorService,
        private messageService: MessageService,
        private tabSharedState: TabSharedStateService,
    ) {}

    ngAfterViewInit(): void {
        this.userApproval.debouncedGetOptions('');
        this.setUserFilterOptions(this.userApproval);
    }

    setUserFilterOptions(...combos: ComboboxNonRSQLComponent[]) {
        combos.forEach((combo) => {
            const origDebounce = combo.debouncedGetOptions.bind(combo);
            combo.filterOptions = (term: string) => {
                const rsql = `email==*${term}*`;
                origDebounce(rsql);
            };
        });
    }

    ngOnDestroy(): void {
        console.log('🧹 [BorComponent] Unmounted');
    }
    ngOnInit(): void {
        console.log('🧹 [BorComponent] Init');
        this.instructionId = this.productInstructionId;
        this.initForm(this.oldBor);
        this.name = 'BOR-' + this.currentProduct?.tradeName + '-' + this.currentProduct?.vnptManPn;
        this.tabSharedState
            .getProductInstructionId$()
            .pipe(takeUntil(this.destroy$))
            .subscribe((id) => {
                if (id) {
                    this.instructionId = id;
                    this.getBor();
                } else {
                    this.getBor();
                }
            });
        this.tabSharedState
            .getMode$()
            .pipe(takeUntil(this.destroy$))
            .subscribe((mode) => {
                this.mode = mode;
            });
        this.tabSharedState
            .getProductVersionId$()
            .pipe(takeUntil(this.destroy$))
            .subscribe((verId) => {
                this.formGroup.patchValue({
                    versionId: verId,
                });
            });

        this.tabSharedState
            .getPhase$()
            .pipe(takeUntil(this.destroy$))
            .subscribe((phase) => {
                this.formGroup.patchValue({
                    phase: phase,
                });
            });
    }

    initForm(data: Bor | null) {
        this.formGroup = new FormGroupCustom<Bor>(this.fb, {
            listMan: new FormArrayCustom(this.initFormContactMan(data?.listMan || [])),
            listLine: new FormArrayCustom(this.initFormContactLine(data?.listLine || [])),
            listMaterial: new FormArrayCustom(this.initFormContact(data?.listMaterial || [])),
            reviewerIds: [data?.reviewerIds],
        });
    }

    get listMan(): FormArrayCustom<FormGroup> {
        return this.formGroup.get('listMan') as FormArrayCustom<FormGroup>;
    }

    get listLine(): FormArrayCustom<FormGroup> {
        return this.formGroup.get('listLine') as FormArrayCustom<FormGroup>;
    }

    get listMaterial(): FormArrayCustom<FormGroup> {
        return this.formGroup.get('listMaterial') as FormArrayCustom<FormGroup>;
    }

    initFormContactMan(items: ManDetail[]): FormGroup[] {
        return items.map(
            (item) =>
                new FormGroupCustom(this.fb, {
                    id: [item?.id],
                    borSummaryId: [item?.borSummaryId],
                    manDirect: [item?.manDirect],
                    manDirectComputedBy: [item?.manDirectComputedBy],
                    manDirectComputedAt: [item?.manDirectComputedAt],
                    manIpqc: [item?.manIpqc],
                    manIpqcComputedBy: [item?.manIpqcComputedBy],
                    manIpqcComputedAt: [item?.manIpqcComputedAt],
                    manFqc: [item?.manFqc],
                    manFqcComputedBy: [item?.manFqcComputedBy],
                    manFqcComputedAt: [item?.manFqcComputedAt],
                    manRepair: [item?.manRepair],
                    manRepairComputedBy: [item?.manRepairComputedBy],
                    manRepairComputedAt: [item?.manRepairComputedAt],
                    manEngineer: [item?.manEngineer],
                    manEngineerComputedBy: [item?.manEngineerComputedBy],
                    manEngineerComputedAt: [item?.manEngineerComputedAt],
                    manMaterial: [item?.manMaterial],
                    manMaterialComputedBy: [item?.manMaterialComputedBy],
                    manMaterialComputedAt: [item?.manMaterialComputedAt],
                    action: [item?.action],
                }),
        );
    }

    initFormContactLine(items: LineDetail[]): FormGroup[] {
        return items.map(
            (item) =>
                new FormGroupCustom(this.fb, {
                    id: [item?.id],
                    borSummaryId: [item?.borSummaryId],
                    equipId: [item?.equipId],
                    section: [item?.section],
                    norm: [item?.norm],
                    unit: [item?.unit],
                    computedBy: [item?.computedBy],
                    computedAt: [item?.computedAt],
                    action: [item?.action],
                }),
        );
    }

    initFormContact(items: any[]): FormGroup[] {
        return items.map(
            (item) =>
                new FormGroupCustom(this.fb, {
                    id: [item?.id],
                    borSummaryId: [item?.borSummaryId],
                }),
        );
    }

    addItem(type: number) {
        if (type === 1) {
            let newItem = null;
            newItem = new FormGroupCustom(this.fb, {
                id: [null],
                borSummaryId: [null],
                manDirect: [null],
                manDirectComputedBy: [null],
                manDirectComputedAt: [null],
                manIpqc: [null],
                manIpqcComputedBy: [null],
                manIpqcComputedAt: [null],
                manFqc: [null],
                manFqcComputedBy: [null],
                manFqcComputedAt: [null],
                manRepair: [null],
                manRepairComputedBy: [null],
                manRepairComputedAt: [null],
                manEngineer: [null],
                manEngineerComputedBy: [null],
                manEngineerComputedAt: [null],
                manMaterial: [null],
                manMaterialComputedBy: [null],
                manMaterialComputedAt: [null],
                action: [1],
            });
            this.listMan.push(newItem);
        } else if (type === 2) {
            let newItem = null;
            newItem = new FormGroupCustom(this.fb, {
                id: [null],
                borSummaryId: [null],
                equipId: [null],
                section: [null],
                norm: [null],
                unit: [null],
                computedBy: [null],
                computedAt: [null],
                action: [1],
            });
            this.listLine.push(newItem);
        }
    }

    handleApproverChange(event: any) {
        this.formGroup.patchValue({
            reviewerIds: event.map((item: any) => item.id),
        });
    }

    getBor() {
        let newItem = null;
        newItem = new FormGroupCustom(this.fb, {
            id: [null],
            borSummaryId: [null],
        });
        this.listMaterial.push(newItem);
        this.borService.getBor(this.instructionId).subscribe({
            next: (res) => {
                this.detailBor = res;
                this.initForm(this.detailBor);
            },
            error: () => {},
        });
    }

    handlePreview() {
        const payload = {
            tabType: this.tabType,
        };
        this.borService.previewBor(payload, this.instructionId).subscribe({
            next: (res) => {
                this.messageService.add({
                    key: 'app-alert',
                    severity: 'success',
                    summary: 'Thành công',
                    detail: 'Gửi review thành công',
                });
                this.getBor();
            },
            error: () => {},
        });
    }

    handleComplete() {
        const payload = {
            tabType: this.tabType,
            approvalStatus: 8,
        };
        this.borService.comfirmBor(payload, this.instructionId).subscribe({
            next: (res) => {
                this.messageService.add({
                    key: 'app-alert',
                    severity: 'success',
                    summary: 'Thành công',
                    detail: 'Phê duyệt thành công',
                });
                this.getBor();
            },
            error: () => {},
        });
    }

    handleReject() {
        const payload = {
            tabType: this.tabType,
            approvalStatus: 4,
        };
        this.borService.comfirmBor(payload, this.instructionId).subscribe({
            next: (res) => {
                this.messageService.add({
                    key: 'app-alert',
                    severity: 'success',
                    summary: 'Thành công',
                    detail: 'Từ chối thành công',
                });
                this.getBor();
            },
            error: () => {},
        });
    }

    handleSubmit() {
        const payload = {
            reviewerIds: this.formGroup.get('reviewerIds')?.value,
            listMan: this.listMan.value,
            listLine: this.listLine.value,
            listMaterial: this.listMaterial.value,
        };
        console.log('payload', payload);
    }
}
