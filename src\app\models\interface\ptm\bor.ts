import { BaseEntity } from '../../BaseEntity';

export interface Bor extends BaseEntity {
    reviewerIds: number[];
    listMan: ManDetail[];
    listLine: LineDetail[];
    listMaterial: MaterialDetail[];
}

export interface ManDetail extends BaseEntity {
    id: number;
    borSummaryId: number;
    manDirect: number;
    manDirectComputedBy: number;
    manDirectComputedAt: number;
    manIpqc: number;
    manIpqcComputedBy: number;
    manIpqcComputedAt: number;
    manFqc: number;
    manFqcComputedBy: number;
    manFqcComputedAt: number;
    manRepair: number;
    manRepairComputedBy: number;
    manRepairComputedAt: number;
    manEngineer: number;
    manEngineerComputedBy: number;
    manEngineerComputedAt: number;
    manMaterial: number;
    manMaterialComputedBy: number;
    manMaterialComputedAt: number;
    action: number;
}

export interface LineDetail extends BaseEntity {
    id: number;
    borSummaryId: number;
    equipId: number;
    section: number;
    norm: number;
    unit: string;
    computedBy: number;
    computedAt: number;
    action: number;
}

export interface MaterialDetail extends BaseEntity {
    id: number;
    manBomId: number;
    instructionId: number;
    pfmeaId: number;
    workStandardId: number;
    description: number;
    unit: string;
    section: number;
    quantity: string;
    reference: string;
    materialType: number;
    consumptionRate: string;
    note: string;
    action: number;
}
