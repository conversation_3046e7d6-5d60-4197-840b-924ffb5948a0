import { HttpClient, HttpErrorResponse } from '@angular/common/http';
import { inject, Injectable } from '@angular/core';
import { catchError, Observable, throwError } from 'rxjs';
import { Bor } from 'src/app/models/interface/ptm/bor';

@Injectable({ providedIn: 'root' })
export class BorService {
    path = '/pr/api/production-instruction';
    #http = inject(HttpClient);

    constructor(private http: HttpClient) {}

    getBor(id: number): Observable<Bor[]> {
        const url = `${this.path}/${id}/bor`;
        return this.#http.get<Bor[]>(url);
    }

    create(formData: any, instructionId: number): Observable<any> {
        const url = `${this.path}/${instructionId}/bor`;
        return this.http.post(url, formData).pipe(catchError(this.handleError));
    }

    exportBor(id: number) {
        const url = `${this.path}/${id}/bor/export`;
        return this.#http.get(url, { responseType: 'text' }).pipe(catchError(this.handleError));
    }

    previewBor(formData: any, id: number) {
        const url = `/pr/api/approval/production-instruction/${id}`;
        return this.#http.post(url, formData).pipe(catchError(this.handleError));
    }

    comfirmBor(formData: any, id: number) {
        const url = `/pr/api/approval/production-instruction/${id}/confirm`;
        return this.#http.post(url, formData).pipe(catchError(this.handleError));
    }

    /** Xử lý chung lỗi HTTP */
    private handleError(error: HttpErrorResponse) {
        // có thể tuỳ chỉnh thông báo, logging, …
        console.error('API error', error);
        return throwError(() => error);
    }
}
