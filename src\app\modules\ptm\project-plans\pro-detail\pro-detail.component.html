<p-panel header="Chi tiết kế hoạch dự án">
    <ng-template pTemplate="icons">
        <div class="tw-flex tw-items-center tw-gap-2 tw-mr-2">
            <p-button label="Thiết lập ngày làm việc" size="small" severity="info" (click)="openWorkingDay()"></p-button>
            <p-button label="Xuất Excel" size="small" severity="success" (click)="exportExcel()"></p-button>
        </div>
    </ng-template>

    <!-- <ng-template> -->
    <app-from [formGroup]="formArray">
        <p-table [value]="visibleTasks" [scrollable]="true" [resizableColumns]="true">
            <ng-template pTemplate="header">
                <tr>
                    <th>STT</th>
                    <th style="min-width: 16rem">Task Name</th>
                    <th style="min-width: 20rem">Timeline</th>
                    <th>Duration (days)</th>
                    <th style="min-width: 12rem">Predecessor</th>
                    <th style="min-width: 15rem">Người phụ trách</th>
                    <th>Tiế<PERSON> độ (%)</th>
                    <th style="min-width: 15rem">Trạng thái</th>
                    <th style="min-width: 15rem">Ghi chú</th>
                    <th>Thao tác</th>
                </tr>
            </ng-template>
            <ng-template pTemplate="body" let-control let-rowIndex="rowIndex">
                <tr [formGroup]="control" [ngClass]="getRowClassBySTT(control)">
                    <td>
                        <app-form-item label="">{{ control.get('displaySTT')?.value }}</app-form-item>
                    </td>

                    <td>
                        <app-form-item label="" validateTrigger="touched">
                            <input
                                type="text"
                                class="tw-w-full"
                                pInputText
                                maxlength="200"
                                formControlName="name"
                                [readonly]="isViewOnly"
                                (change)="onTaskNameChange(rowIndex)"
                            />
                            <div *ngIf="control.get('name')?.hasError('duplicateTaskName')" class="tw-text-red-500 tw-text-sm">
                                {{ control.get('name')?.getError('duplicateTaskName')?.message }}
                            </div>
                        </app-form-item>
                    </td>
                    <td>
                        <app-form-item label="" validateTrigger="touched">
                            <p-calendar
                                #calendarRef
                                formControlName="timeline"
                                selectionMode="range"
                                [readonlyInput]="true"
                                class="tw-w-full"
                                [showIcon]="true"
                                appendTo="body"
                                dateFormat="dd/mm/yy"
                                placeholder="Từ... - Đến..."
                                (onSelect)="onDateRangeSelect(rowIndex, calendarRef)"
                            ></p-calendar>

                            <div *ngIf="control.get('timeline')?.errors?.outsideProjectStart" class="tw-text-red-500 tw-text-sm">
                                {{ control.get('timeline')?.errors?.outsideProjectStart }}
                            </div>

                            <div *ngIf="control.get('timeline')?.errors?.outsideProjectEnd" class="tw-text-red-500 tw-text-sm">
                                {{ control.get('timeline')?.errors?.outsideProjectEnd }}
                            </div>
                        </app-form-item>
                    </td>
                    <td style="text-align: center">
                        <app-form-item>{{ control.get('duration')?.value }}</app-form-item>
                    </td>
                    <td>
                        <app-form-item label="">
                            <input
                                type="text"
                                class="tw-w-full"
                                pInputText
                                formControlName="predecessor"
                                [readonly]="control.get('level')?.value > 1 || isViewOnly"
                                (keypress)="allowOnlyPredecessorChars($event)"
                                appendTo="body"
                            />
                            <div
                                *ngIf="control.get('predecessor')?.errors?.invalidSTT && control.get('predecessor')?.touched"
                                class="tw-text-red-500 tw-text-sm"
                                style="font-size: 13px"
                            >
                                Vui lòng nhập STT hợp lệ
                            </div>
                        </app-form-item>
                    </td>
                    <td>
                        <app-form-item label="">
                            <app-combobox-nonRSQL
                                #userFilterRef
                                [fetchOnInit]="false"
                                type="select-one"
                                formControlName="assignee"
                                fieldValue="id"
                                fieldLabel="fullName"
                                url="/auth/api/users/simple-search"
                                param="query"
                                [initSearch]="control.get('assigneeName')?.value"
                                placeholder="Chọn giá trị"
                                [additionalParams]="{ size: 100 }"
                            >
                            </app-combobox-nonRSQL>
                        </app-form-item>
                    </td>
                    <td>
                        <input
                            type="text"
                            class="tw-w-full"
                            pInputText
                            formControlName="progress"
                            (keypress)="allowOnlyPredecessorChars($event)"
                            (blur)="validateProgress(rowIndex)"
                            [readonly]="isProgressReadOnly(rowIndex) || isViewOnly"
                        />
                    </td>
                    <td>
                        <app-form-item label="">
                            <p-dropdown
                                [options]="statusOptions"
                                appendTo="body"
                                class="tw-w-full"
                                formControlName="status"
                                optionLabel="label"
                                optionValue="value"
                                [readonly]="isViewOnly"
                                (onChange)="onStatusChange(rowIndex)"
                            ></p-dropdown>
                        </app-form-item>
                    </td>
                    <td>
                        <app-form-item label="">
                            <input type="text" pInputText class="tw-w-full" maxlength="500" formControlName="note" [readonly]="isViewOnly" />
                        </app-form-item>
                    </td>
                    <td>
                        <button
                            pButton
                            type="button"
                            icon="pi pi-ellipsis-h"
                            class="p-button-text p-button-sm"
                            [disabled]="isViewOnly"
                            (click)="onMenuClick($event, rowIndex, menu)"
                        ></button>
                        <p-menu #menu [popup]="true" [model]="menuItems" appendTo="body"></p-menu>
                    </td>
                </tr>
            </ng-template>
        </p-table>
    </app-from>
    <p-button label="Thêm task" size="small" severity="info" (click)="addTask()" [disabled]="isViewOnly"></p-button>
</p-panel>

<app-popup
    #WorkingDayPopup
    header="THIẾT LẬP NGÀY LÀM VIỆC"
    [isButtonVisible]="false"
    dialogWidth="70vw"
    [showConfirmButton]="false"
    (onClose)="resetSelectedDateDetails()"
>
    <div class="workday-dialog-content tw-grid lg:tw-grid-cols-[200px_1fr_200px] tw-grid-cols-1 tw-gap-4">
        <div class="legend-section">
            <h3>Bảng chú thích</h3>
            <div class="legend-items">
                <div class="legend-item">
                    <div class="calendar-box working">01</div>
                    <span>Working</span>
                </div>
                <div class="legend-item">
                    <div class="calendar-box nonworking">01</div>
                    <span>Nonworking</span>
                </div>
            </div>
        </div>
        <div class="calendar-section tw-flex tw-flex-col tw-items-center tw-text-center tw-w-full">
            <h3>Chọn ngày để xem thiết lập</h3>
            <!-- <div class="tw-w-full"> -->
            <p-calendar
                [(ngModel)]="selectedDates"
                class="tw-w-full"
                [inline]="true"
                [panelStyleClass]="'compact-week-calendar'"
                [firstDayOfWeek]="0"
                (onSelect)="onDateClick($event)"
            >
            </p-calendar>
            <!-- </div> -->
            <div class="tw-text-gray-500">Lưu ý: Default Weekend = Nonworking</div>
        </div>
        <div class="detail-section">
            <h3 class="tw-flex tw-flex-col tw-items-center tw-text-center tw-w-full">Nội dung thiết lập</h3>
            <!-- <div *ngIf="selectedDateDetails.length">
                <span *ngFor="let detail of selectedDateDetails" class="tw-block tw-mb-1">{{ detail.content }}</span>
            </div> -->
            <div *ngIf="selectedDate" class="tw-mt-2 tw-text-base tw-font-medium">
                <span class="tw-font-semibold">{{ selectedSettingContent }}</span>
            </div>
        </div>
    </div>
    <div class="workday-dialog-content">
        <h3>Bảng thiết lập</h3>
        <app-from [formGroup]="settingsForm">
            <p-table [value]="settingsForm.controls" [scrollable]="true" [resizableColumns]="true" [tableStyle]="{ 'table-layout': 'auto', width: '100%' }">
                <ng-template pTemplate="header">
                    <tr>
                        <th style="width: 4rem; text-align: center">STT</th>
                        <th style="width: 13rem; text-align: center">Nội dung</th>
                        <th style="width: 13rem; text-align: center">Loại</th>
                        <th style="text-align: center">Khoảng thời gian</th>
                        <th style="text-align: center">Ngày cụ thể</th>
                        <th style="text-align: center">
                            <button pButton type="button" icon="pi pi-plus" class="p-button-secondary p-button-sm-add" (click)="addSettingRow()"></button>
                        </th>
                    </tr>
                </ng-template>
                <ng-template pTemplate="body" let-setting let-rowIndex="rowIndex">
                    <tr [formGroup]="setting">
                        <td style="text-align: center">{{ rowIndex + 1 }}</td>
                        <td>
                            <app-form-item label="" class="tw-w-[110px]">
                                <input pInputText formControlName="content" type="text" style="width: 11rem" />
                                <div *ngIf="setting.get('content')?.touched && setting.get('content')?.hasError('required')">
                                    <span class="tw-text-red-500"> Trường này là bắt buộc. </span>
                                </div>
                            </app-form-item>
                        </td>
                        <td>
                            <app-form-item label="">
                                <p-dropdown
                                    formControlName="dayType"
                                    [options]="dayTypes"
                                    placeholder="Chọn giá trị"
                                    class="tw-w-full"
                                    optionLabel="label"
                                    optionValue="value"
                                    appendTo="body"
                                ></p-dropdown>
                                <div *ngIf="setting.get('dayType')?.touched && setting.get('dayType')?.hasError('required')">
                                    <span class="tw-text-red-500"> Trường này là bắt buộc. </span>
                                </div>
                            </app-form-item>
                        </td>
                        <td>
                            <div class="tw-flex tw-items-center tw-gap-2">
                                <p-checkbox formControlName="enableDateRange" binary="true" (onChange)="onDateRangeToggle(setting)"></p-checkbox>
                                <app-form-item label="">
                                    <p-calendar
                                        formControlName="rangeDates"
                                        selectionMode="range"
                                        [readonlyInput]="true"
                                        [showIcon]="true"
                                        appendTo="body"
                                        dateFormat="dd/mm/yy"
                                        placeholder="Từ... - Đến..."
                                    ></p-calendar>
                                </app-form-item>
                            </div>
                            <div *ngIf="isSettingSubmitted(setting) && setting.invalid">
                                <span *ngIf="setting.errors?.atLeastOneRequired" class="tw-text-red-500 tw-ml-6">Phải chọn ít nhất 1 loại ngày.</span>
                                <span *ngIf="setting.errors?.rangeDatesRequired" class="tw-text-red-500 tw-ml-6"> Vui lòng chọn khoảng thời gian. </span>
                            </div>
                        </td>
                        <td>
                            <div class="tw-flex tw-items-center tw-gap-2">
                                <p-checkbox formControlName="enableSpecificDate" binary="true" (onChange)="onDateRangeToggle(setting)"></p-checkbox>

                                <p-calendar
                                    formControlName="specificDate"
                                    [showIcon]="true"
                                    placeholder="Chọn ngày"
                                    dateFormat="dd/mm/yy"
                                    appendTo="body"
                                ></p-calendar>
                            </div>
                            <div *ngIf="isSettingSubmitted(setting) && setting.invalid">
                                <span *ngIf="setting.errors?.atLeastOneRequired" class="tw-text-red-500 tw-ml-6">Phải chọn ít nhất 1 loại ngày.</span>
                                <span *ngIf="setting.errors?.specificDateRequired" class="tw-text-red-500 tw-ml-6"> Vui lòng chọn ngày cụ thể. </span>
                            </div>
                        </td>
                        <td>
                            <div class="tw-flex tw-items-center tw-justify-center tw-space-x-2 text-center">
                                <button
                                    pButton
                                    type="button"
                                    icon="pi pi-minus"
                                    style="color: white"
                                    class="p-button-sm-add p-button-danger tw-text-white"
                                    (click)="deleteRow(rowIndex)"
                                ></button>
                            </div>
                        </td>
                    </tr>
                    <!-- <tr *ngIf="setting.invalid && (setting.dirty || setting.touched)">
                        <td colspan="6" class="tw-text-red-500 tw-text-sm tw-px-4 tw-pb-2">
                            <div *ngIf="setting.errors?.atLeastOneRequired">Phải chọn ít nhất 1 loại ngày.</div>
                            <div *ngIf="setting.errors?.rangeDatesRequired">Vui lòng chọn khoảng thời gian hợp lệ.</div>
                            <div *ngIf="setting.errors?.specificDateRequired">Vui lòng chọn ngày cụ thể.</div>
                        </td>
                    </tr> -->
                </ng-template>
            </p-table>
        </app-from>
    </div>
    <div class="tw-absolute tw-right-[90px]" style="bottom: 20px">
        <button pButton label="Áp dụng" class="p-button-sm p-button-secondary" (click)="onApply()"></button>
    </div>
</app-popup>
