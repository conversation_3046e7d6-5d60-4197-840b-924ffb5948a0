<div class="card-content" *ngIf="title !== 'BOR'">
    <label class="label" for="pf">Thông tin chung</label>

    <div class="tw-grid [grid-template-columns:auto_1fr] tw-gap-y-4 tw-gap-x-4">
        <div class="tw-font-semibold">Tên {{ title }}:</div>
        <div class="tw-font-semibold">{{ name }}</div>

        <div class="tw-font-semibold">Ngày tạo:</div>
        <div class="tw-font-semibold tw-truncate" tooltipPosition="top">
            {{ detailInfo?.instructionInfo?.createdAt | date: 'dd/MM/yyyy' }}
        </div>

        <div class="tw-font-semibold">Người tạo:</div>
        <div class="tw-font-semibold tw-truncate" tooltipPosition="top">
            {{ detailInfo?.instructionInfo?.creator?.fullName }}
        </div>

        <div class="tw-font-semibold">Trạng thái:</div>
        <div class="tw-font-semibold">
            {{ STATUS_MAP[detailInfo?.instructionInfo?.status] ?? 'Draft' }}
        </div>
        <div class="tw-font-semibold">Người soát xét:</div>
        <div class="tw-max-w-[500px]">
            <app-form #form [formGroup]="formGroup">
                <app-custom-form-item [control]="">
                    <app-combobox-nonRSQL
                        #userApproval
                        [fetchOnInit]="true"
                        type="select"
                        formControlName="reviewers"
                        fieldValue="id"
                        fieldLabel="email"
                        url="/auth/api/users/simple-search"
                        param="query"
                        [additionalParams]="{ page: 0, size: 100 }"
                        (onChange)="changeApprover($event)"
                    >
                    </app-combobox-nonRSQL>
                </app-custom-form-item>
            </app-form>
        </div>
    </div>
</div>

<div class="card-content" *ngIf="title === 'BOR'">
    <label class="label" for="pf">Thông tin chung</label>

    <div class="tw-grid tw-grid-cols-3 tw-gap-6">
        <!-- Cột 1: Người tạo -->
        <div class="tw-grid [grid-template-columns:auto_1fr] tw-gap-x-3 tw-gap-y-4">
            <div class="tw-font-semibold">Tên BOR:</div>
            <div class="tw-font-semibold">{{ name }}</div>

            <div class="tw-font-semibold">Ngày tạo:</div>
            <div class="tw-font-semibold">{{ detailInfo?.instructionInfo?.createdAt | date: 'HH:mm dd/MM/yyyy' }}</div>

            <div class="tw-font-semibold">Người tạo:</div>
            <div class="tw-font-semibold">{{ detailInfo?.instructionInfo?.creator?.fullName }}</div>

            <div class="tw-font-semibold">Chức vụ:</div>
            <div class="tw-font-semibold">[Chức vụ]</div>

            <div class="tw-font-semibold">Chữ ký người tạo:</div>
            <div class="tw-font-semibold">[Hình ảnh/tên chữ ký]</div>
        </div>

        <!-- Cột 2: Soát xét -->
        <div class="tw-grid [grid-template-columns:auto_1fr] tw-gap-x-3 tw-gap-y-4">
            <div class="tw-font-semibold">Trạng thái soát xét:</div>
            <div class="tw-font-semibold">{{ STATUS_MAP[detailInfo?.instructionInfo?.reviewStatus] ?? 'Draft' }}</div>

            <div class="tw-font-semibold">Ngày soát xét:</div>
            <div class="tw-font-semibold">{{ detailInfo?.instructionInfo?.reviewDate | date: 'dd/MM/yyyy' }}</div>

            <div class="tw-font-semibold">Người soát xét:</div>
            <div>
                <app-form [formGroup]="formGroup">
                    <app-custom-form-item [control]="formGroup.get('reviewers')" class="tw-mb-0">
                        <app-combobox-nonRSQL
                            class="tw-w-[240px]"
                            #userApproval
                            [fetchOnInit]="true"
                            type="select"
                            formControlName="reviewers"
                            fieldValue="id"
                            fieldLabel="email"
                            url="/auth/api/users/simple-search"
                            param="query"
                            [additionalParams]="{ page: 0, size: 100 }"
                            (onChange)="changeApprover($event)"
                        >
                        </app-combobox-nonRSQL>
                    </app-custom-form-item>
                </app-form>
            </div>

            <div class="tw-font-semibold">Chức vụ:</div>
            <div class="tw-font-semibold">[Chức vụ]</div>

            <div class="tw-font-semibold">Chữ ký người soát xét:</div>
            <div class="tw-font-semibold">[Hình ảnh/tên chữ ký]</div>
        </div>

        <!-- Cột 3: Phê duyệt -->
        <div class="tw-grid [grid-template-columns:auto_1fr] tw-gap-x-3 tw-gap-y-4">
            <div class="tw-font-semibold">Trạng thái phê duyệt:</div>
            <div class="tw-font-semibold">{{ STATUS_MAP[detailInfo?.instructionInfo?.approveStatus] ?? 'Draft' }}</div>

            <div class="tw-font-semibold">Ngày phê duyệt:</div>
            <div class="tw-font-semibold">{{ detailInfo?.instructionInfo?.approveDate | date: 'dd/MM/yyyy' }}</div>

            <div class="tw-font-semibold">Người phê duyệt:</div>
            <div>
                <app-form [formGroup]="formGroup">
                    <app-custom-form-item [control]="formGroup.get('reviewers')" class="tw-mb-0">
                        <app-combobox-nonRSQL
                            class="tw-w-[240px]"
                            #userApproval
                            [fetchOnInit]="true"
                            type="select"
                            formControlName="reviewers"
                            fieldValue="id"
                            fieldLabel="email"
                            url="/auth/api/users/simple-search"
                            param="query"
                            [additionalParams]="{ page: 0, size: 100 }"
                            (onChange)="changeApprover($event)"
                        >
                        </app-combobox-nonRSQL>
                    </app-custom-form-item>
                </app-form>
            </div>

            <div class="tw-font-semibold">Chức vụ:</div>
            <div class="tw-font-semibold">[Chức vụ]</div>

            <div class="tw-font-semibold">Chữ ký người phê duyệt:</div>
            <div class="tw-font-semibold">[Hình ảnh/tên chữ ký]</div>
        </div>
    </div>
</div>
